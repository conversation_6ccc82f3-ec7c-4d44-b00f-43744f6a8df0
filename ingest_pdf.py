#!/usr/bin/env python3
"""
PDF Ingestion Script with Table Detection, Image Extraction, and Vector Storage

This script processes PDF files by:
1. Extracting text and detecting tables using pdfplumber
2. Extracting images using PyMuPDF
3. Using OpenAI GPT-4 to structure table data
4. Generating embeddings and storing in Supabase with pgvector
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import uuid

import fitz  # PyMuPDF
import pdfplumber
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client

# Load environment variables
load_dotenv()

class PDFProcessor:
    def __init__(self):
        """Initialize the PDF processor with API clients."""
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.llm_model = os.getenv("LLM_MODEL", "gpt-4")
        
        # Statistics
        self.stats = {
            "total_pages": 0,
            "pages_with_tables": 0,
            "total_images": 0
        }
        
    def setup_database(self):
        """Create the pdf_documents table if it doesn't exist."""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS pdf_documents (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            file_name TEXT,
            page_number INTEGER,
            text TEXT,
            metadata JSONB,
            embedding VECTOR(1536),
            created_at TIMESTAMP DEFAULT NOW()
        );
        """

        try:
            # The table should already be created, but we'll verify it exists
            print("✅ Database table 'pdf_documents' ready")
        except Exception as e:
            print(f"⚠️  Database setup warning: {e}")
            print("Continuing with existing table structure...")

    def extract_images_from_page(self, pdf_path: str, page_num: int) -> List[str]:
        """Extract and save images from a PDF page using PyMuPDF."""
        image_paths = []
        
        try:
            # Open PDF with PyMuPDF
            pdf_document = fitz.open(pdf_path)
            page = pdf_document[page_num]
            
            # Get images from the page
            image_list = page.get_images()
            
            # Create images directory if it doesn't exist
            images_dir = Path("./images")
            images_dir.mkdir(exist_ok=True)
            
            for img_index, img in enumerate(image_list):
                # Get image data
                xref = img[0]
                pix = fitz.Pixmap(pdf_document, xref)
                
                # Convert to PNG if not already
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_filename = f"page{page_num + 1}_img{img_index + 1}.png"
                    img_path = images_dir / img_filename
                    pix.save(str(img_path))
                    image_paths.append(str(img_path))
                else:  # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    img_filename = f"page{page_num + 1}_img{img_index + 1}.png"
                    img_path = images_dir / img_filename
                    pix1.save(str(img_path))
                    image_paths.append(str(img_path))
                    pix1 = None
                
                pix = None
            
            pdf_document.close()
            self.stats["total_images"] += len(image_paths)
            
        except Exception as e:
            print(f"⚠️  Error extracting images from page {page_num + 1}: {e}")
        
        return image_paths

    def detect_and_structure_tables(self, page_text: str, has_tables: bool) -> Optional[Dict]:
        """Use OpenAI GPT-4 to structure table data from page text."""
        if not has_tables:
            return None
            
        prompt = f"""Given this PDF page text, extract any table you see into JSON.
Guess the table title based on context.
If this appears to be a continuation from a previous page (no clear header/title), set "is_continuation": true.

Return ONLY valid JSON in this exact format:
{{
    "is_continuation": true/false,
    "table_title": "Some descriptive title",
    "rows": [
        {{"Column1": "value1", "Column2": "value2"}},
        {{"Column1": "value3", "Column2": "value4"}}
    ]
}}

Page text:
{page_text}"""

        try:
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": "You are a table extraction expert. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            table_json = json.loads(response.choices[0].message.content.strip())
            return table_json
            
        except Exception as e:
            print(f"⚠️  Error processing table with LLM: {e}")
            return None

    def create_markdown_content(self, page_num: int, page_text: str, 
                              table_data: Optional[Dict], image_paths: List[str]) -> str:
        """Generate markdown content for a page."""
        markdown_parts = [f"📄 Page {page_num + 1}"]
        
        # Add table section if present
        if table_data:
            title = table_data.get("table_title", "Untitled Table")
            markdown_parts.append(f"🧾 Table: {title}")
            
            # Convert JSON rows to markdown table
            rows = table_data.get("rows", [])
            if rows:
                # Get headers from first row
                headers = list(rows[0].keys())
                header_row = "| " + " | ".join(headers) + " |"
                separator_row = "| " + " | ".join(["---"] * len(headers)) + " |"
                
                markdown_parts.extend([header_row, separator_row])
                
                # Add data rows
                for row in rows:
                    data_row = "| " + " | ".join(str(row.get(h, "")) for h in headers) + " |"
                    markdown_parts.append(data_row)
        
        # Add images section if present
        if image_paths:
            markdown_parts.append("🖼️ Images")
            for img_path in image_paths:
                markdown_parts.append(f"- {img_path}")
        
        # Add full page text
        markdown_parts.extend([
            "📚 Full Page Text",
            page_text
        ])
        
        return "\n\n".join(markdown_parts)

    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"⚠️  Error generating embedding: {e}")
            return [0.0] * 1536  # Return zero vector as fallback

    def process_pdf(self, pdf_path: str) -> bool:
        """Process a PDF file and ingest into Supabase."""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            print(f"❌ PDF file not found: {pdf_path}")
            return False
        
        print(f"🔄 Processing PDF: {pdf_path.name}")
        
        # Setup database
        self.setup_database()
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                self.stats["total_pages"] = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages):
                    print(f"📄 Processing page {page_num + 1}/{len(pdf.pages)}")
                    
                    # Extract text
                    page_text = page.extract_text() or ""
                    
                    # Detect tables
                    tables = page.find_tables()
                    has_tables = len(tables) > 0
                    if has_tables:
                        self.stats["pages_with_tables"] += 1
                    
                    # Extract images
                    image_paths = self.extract_images_from_page(str(pdf_path), page_num)
                    
                    # Structure tables with LLM
                    table_data = self.detect_and_structure_tables(page_text, has_tables)
                    
                    # Generate markdown content
                    markdown_content = self.create_markdown_content(
                        page_num, page_text, table_data, image_paths
                    )
                    
                    # Generate embedding
                    embedding = self.generate_embedding(markdown_content)
                    
                    # Prepare metadata
                    metadata = {
                        "type": "table" if has_tables else "text",
                        "table_id": str(uuid.uuid4()) if table_data else None,
                        "table_title": table_data.get("table_title") if table_data else None,
                        "image_paths": image_paths,
                        "is_continuation": table_data.get("is_continuation", False) if table_data else False
                    }
                    
                    # Insert into Supabase
                    try:
                        self.supabase.table("pdf_documents").insert({
                            "file_name": pdf_path.name,
                            "page_number": page_num + 1,
                            "text": markdown_content,
                            "metadata": metadata,
                            "embedding": embedding
                        }).execute()
                        
                    except Exception as e:
                        print(f"⚠️  Error inserting page {page_num + 1}: {e}")
                        continue
                
                return True
                
        except Exception as e:
            print(f"❌ Error processing PDF: {e}")
            return False

    def print_summary(self):
        """Print processing summary."""
        print("\n" + "="*50)
        print("📊 PROCESSING SUMMARY")
        print("="*50)
        print(f"📄 Total pages processed: {self.stats['total_pages']}")
        print(f"🧾 Pages with tables: {self.stats['pages_with_tables']}")
        print(f"🖼️ Total images extracted: {self.stats['total_images']}")
        print("="*50)

def main():
    """Main function to handle CLI arguments and process PDF."""
    parser = argparse.ArgumentParser(description="Ingest PDF with table detection and vector storage")
    parser.add_argument("pdf_path", help="Path to the PDF file to process")
    
    args = parser.parse_args()
    
    # Validate environment variables
    required_env_vars = ["OPENAI_API_KEY", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
        sys.exit(1)
    
    # Process PDF
    processor = PDFProcessor()
    success = processor.process_pdf(args.pdf_path)
    
    # Print summary
    processor.print_summary()
    
    if success:
        print("✅ PDF processing completed successfully!")
        sys.exit(0)
    else:
        print("❌ PDF processing failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()

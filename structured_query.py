#!/usr/bin/env python3
"""
Structured Query App with Gradio

This app allows users to:
1. Paste a JSON schema
2. Enter a natural language instruction
3. Specify a file name to search within
4. Extract structured JSON data using OpenAI and Supabase vector search
"""

import json
import os
import csv
import io
from typing import List, Dict, Any, Tuple
import gradio as gr
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client

# Load environment variables
load_dotenv()

class StructuredQueryProcessor:
    def __init__(self):
        """Initialize the processor with API clients."""
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.llm_model = os.getenv("LLM_MODEL", "gpt-4o")

    def generate_subqueries(self, schema: str, instruction: str) -> List[str]:
        """Break down the instruction into 1-3 meaningful subqueries using GPT-4."""
        prompt = f"""Given this JSON schema and extraction instruction, break it down into 1-3 specific, focused subqueries that will help extract the required information.

JSON Schema:
{schema}

Instruction:
{instruction}

Return ONLY a JSON array of 1-3 specific search queries, like:
["query about specific aspect 1", "query about specific aspect 2", "query about specific aspect 3"]

Each query should be focused on extracting specific parts of the schema."""

        try:
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": "You are a query decomposition expert. Return only valid JSON arrays."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            subqueries = json.loads(response.choices[0].message.content.strip())
            return subqueries if isinstance(subqueries, list) else [instruction]
            
        except Exception as e:
            print(f"Error generating subqueries: {e}")
            return [instruction]  # Fallback to original instruction

    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return [0.0] * 1536  # Return zero vector as fallback

    def search_documents(self, query: str, file_name: str, top_k: int = 10) -> List[Dict]:
        """Search documents using vector similarity."""
        try:
            # Generate embedding for the query
            query_embedding = self.generate_embedding(query)
            
            # Call the match_documents function
            result = self.supabase.rpc('match_documents', {
                'query_embedding': query_embedding,
                'match_threshold': 0.1,  # Low threshold to get more results
                'match_count': top_k,
                'input_file_name': file_name
            }).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            print(f"Error searching documents: {e}")
            return []

    def extract_structured_data(self, schema: str, instruction: str, context_chunks: List[str]) -> List[Dict]:
        """Extract structured JSON data from context using GPT-4."""
        # Combine all context chunks
        combined_context = "\n\n---\n\n".join(context_chunks)
        
        prompt = f"""Extract structured data from the following context according to the JSON schema and instruction.

JSON Schema:
{schema}

Instruction:
{instruction}

Context:
{combined_context}

Return a JSON array of objects that conform to the schema. If a value is missing or cannot be determined, use "N/A".
Return ONLY valid JSON - no explanations or additional text.

Example format:
[
  {{"field1": "value1", "field2": "value2"}},
  {{"field1": "value3", "field2": "N/A"}}
]"""

        try:
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": "You are a data extraction expert. Return only valid JSON arrays that conform to the given schema."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            result = json.loads(response.choices[0].message.content.strip())
            return result if isinstance(result, list) else [result]
            
        except Exception as e:
            print(f"Error extracting structured data: {e}")
            return [{"error": f"Failed to extract data: {str(e)}"}]

    def process_query(self, schema: str, instruction: str, file_name: str) -> Tuple[List[Dict], str]:
        """Main processing function that orchestrates the entire workflow."""
        try:
            # Validate inputs
            if not schema.strip():
                return [], "❌ Please provide a JSON schema"
            if not instruction.strip():
                return [], "❌ Please provide an instruction"
            if not file_name.strip():
                return [], "❌ Please provide a file name"
            
            # Validate JSON schema
            try:
                json.loads(schema)
            except json.JSONDecodeError:
                return [], "❌ Invalid JSON schema format"
            
            status_msg = "🔄 Processing query...\n"
            
            # Step 1: Generate subqueries
            status_msg += "📝 Breaking down instruction into subqueries...\n"
            subqueries = self.generate_subqueries(schema, instruction)
            status_msg += f"Generated {len(subqueries)} subqueries\n"
            
            # Step 2: Search for each subquery and collect context
            all_context_chunks = []
            for i, subquery in enumerate(subqueries):
                status_msg += f"🔍 Searching for subquery {i+1}: {subquery[:50]}...\n"
                search_results = self.search_documents(subquery, file_name)
                
                if search_results:
                    # Extract text from search results
                    for result in search_results:
                        if result.get('text'):
                            all_context_chunks.append(result['text'])
                    status_msg += f"Found {len(search_results)} relevant chunks\n"
                else:
                    status_msg += "No relevant chunks found for this subquery\n"
            
            if not all_context_chunks:
                return [], f"{status_msg}❌ No relevant content found in '{file_name}'"
            
            # Remove duplicates while preserving order
            unique_chunks = []
            seen = set()
            for chunk in all_context_chunks:
                if chunk not in seen:
                    unique_chunks.append(chunk)
                    seen.add(chunk)
            
            status_msg += f"📄 Using {len(unique_chunks)} unique context chunks\n"
            
            # Step 3: Extract structured data
            status_msg += "🧠 Extracting structured data with GPT-4...\n"
            structured_data = self.extract_structured_data(schema, instruction, unique_chunks)
            
            status_msg += f"✅ Extracted {len(structured_data)} records"
            
            return structured_data, status_msg
            
        except Exception as e:
            return [], f"❌ Error processing query: {str(e)}"

def create_csv_download(data: List[Dict]) -> str:
    """Create a CSV string from the structured data."""
    if not data:
        return ""
    
    output = io.StringIO()
    if data:
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    return output.getvalue()

def main():
    """Create and launch the Gradio interface."""
    processor = StructuredQueryProcessor()
    
    def process_and_format(schema, instruction, file_name):
        """Process the query and return formatted results."""
        structured_data, status = processor.process_query(schema, instruction, file_name)
        
        # Format JSON for display
        json_output = json.dumps(structured_data, indent=2) if structured_data else "[]"
        
        # Create CSV for download
        csv_data = create_csv_download(structured_data)
        
        return json_output, status, csv_data
    
    # Example schema and instruction
    example_schema = """{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "part_number": {"type": "string"},
      "description": {"type": "string"},
      "price": {"type": "string"},
      "availability": {"type": "string"}
    }
  }
}"""
    
    example_instruction = "Extract all spare parts with their part numbers, descriptions, prices, and availability status from the document."
    
    # Create Gradio interface
    with gr.Blocks(title="Structured Query Extractor", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🔍 Structured Query Extractor")
        gr.Markdown("Extract structured JSON data from PDF documents using natural language instructions and JSON schemas.")
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("## 📝 Input")
                
                schema_input = gr.Textbox(
                    label="JSON Schema",
                    placeholder="Paste your JSON schema here...",
                    lines=10,
                    value=example_schema
                )
                
                instruction_input = gr.Textbox(
                    label="Extraction Instruction",
                    placeholder="Describe what you want to extract...",
                    lines=3,
                    value=example_instruction
                )
                
                file_name_input = gr.Textbox(
                    label="File Name",
                    placeholder="e.g., TANK CLEANING MACHINE.pdf",
                    value="TANK CLEANING MACHINE.pdf"
                )
                
                run_button = gr.Button("🚀 Run Query", variant="primary")
            
            with gr.Column(scale=1):
                gr.Markdown("## 📊 Results")
                
                status_output = gr.Textbox(
                    label="Status",
                    lines=8,
                    interactive=False
                )
                
                json_output = gr.Code(
                    label="Extracted JSON Data",
                    language="json",
                    lines=15
                )
                
                csv_download = gr.File(
                    label="Download CSV",
                    visible=False
                )
        
        # Event handlers
        def handle_query(schema, instruction, file_name):
            json_result, status, csv_data = process_and_format(schema, instruction, file_name)
            
            # Create temporary CSV file for download
            csv_file = None
            if csv_data:
                csv_file = "extracted_data.csv"
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    f.write(csv_data)
            
            return json_result, status, csv_file
        
        run_button.click(
            fn=handle_query,
            inputs=[schema_input, instruction_input, file_name_input],
            outputs=[json_output, status_output, csv_download]
        )
        
        # Show CSV download when data is available
        json_output.change(
            fn=lambda x: gr.update(visible=bool(x and x.strip() != "[]")),
            inputs=[json_output],
            outputs=[csv_download]
        )
    
    return app

if __name__ == "__main__":
    app = main()
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )

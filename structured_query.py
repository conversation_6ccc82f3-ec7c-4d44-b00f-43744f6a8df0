#!/usr/bin/env python3
"""
Structured Query App with Gradio

This app allows users to:
1. Paste a JSON schema
2. Enter a natural language instruction
3. Specify a file name to search within
4. Extract structured JSON data using OpenAI and Supabase vector search
"""

import json
import os
import csv
import io
import re
import logging
from typing import List, Dict, Any, Tuple
import gradio as gr
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def heal_json(text: str) -> str:
    """
    Attempt to heal malformed JSON by applying common fixes.
    """
    logger.info(f"🔧 Attempting to heal JSON: {text[:100]}...")

    # Remove markdown code blocks
    text = re.sub(r'```json\s*', '', text)
    text = re.sub(r'```\s*$', '', text)
    text = re.sub(r'^```\s*', '', text)

    # Remove leading/trailing whitespace
    text = text.strip()

    # Try to find JSON array or object boundaries
    # Look for array pattern
    array_match = re.search(r'\[.*\]', text, re.DOTALL)
    if array_match:
        text = array_match.group(0)
        logger.info("🔧 Extracted array from text")
    else:
        # Look for object pattern
        obj_match = re.search(r'\{.*\}', text, re.DOTALL)
        if obj_match:
            text = obj_match.group(0)
            logger.info("🔧 Extracted object from text")

    # Fix common JSON issues
    # Fix single quotes to double quotes
    text = re.sub(r"'([^']*)':", r'"\1":', text)
    text = re.sub(r":\s*'([^']*)'", r': "\1"', text)

    # Fix trailing commas
    text = re.sub(r',\s*}', '}', text)
    text = re.sub(r',\s*]', ']', text)

    # Fix missing quotes around keys
    text = re.sub(r'(\w+):', r'"\1":', text)

    # Fix double-quoted keys that got double-quoted
    text = re.sub(r'""(\w+)"":', r'"\1":', text)

    logger.info(f"🔧 Healed JSON: {text[:100]}...")
    return text

def safe_json_parse(text: str, fallback_value=None) -> any:
    """
    Safely parse JSON with healing attempts.
    """
    logger.info(f"🔍 Attempting to parse JSON: {len(text)} characters")

    # First attempt: direct parsing
    try:
        result = json.loads(text)
        logger.info("✅ JSON parsed successfully on first attempt")
        return result
    except json.JSONDecodeError as e:
        logger.warning(f"⚠️ Initial JSON parse failed: {e}")

    # Second attempt: heal and parse
    try:
        healed_text = heal_json(text)
        result = json.loads(healed_text)
        logger.info("✅ JSON parsed successfully after healing")
        return result
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON parse failed even after healing: {e}")

    # Third attempt: extract JSON from mixed content
    try:
        # Look for JSON patterns in the text
        json_patterns = [
            r'\[.*\]',  # Array
            r'\{.*\}',  # Object
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    result = json.loads(match)
                    logger.info("✅ JSON extracted and parsed from mixed content")
                    return result
                except:
                    continue
    except Exception as e:
        logger.error(f"❌ Pattern extraction failed: {e}")

    # Final fallback
    logger.error("❌ All JSON parsing attempts failed, using fallback")
    return fallback_value if fallback_value is not None else []

class StructuredQueryProcessor:
    def __init__(self):
        """Initialize the processor with API clients."""
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.supabase: Client = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_KEY")
        )
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        self.llm_model = os.getenv("LLM_MODEL", "gpt-3.5-turbo")

    def test_connections(self) -> Tuple[bool, str]:
        """Test API connections and return status."""
        try:
            # Test OpenAI connection
            print("Testing OpenAI connection...")
            test_response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            print("✅ OpenAI connection successful")

            # Test Supabase connection
            print("Testing Supabase connection...")
            result = self.supabase.table("pdf_documents").select("count", count="exact").limit(1).execute()
            print("✅ Supabase connection successful")

            return True, "All connections successful"

        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            print(f"❌ {error_msg}")
            return False, error_msg

    def generate_subqueries(self, schema: str, instruction: str) -> List[str]:
        """Break down the instruction into 1-3 meaningful subqueries using GPT-4."""
        logger.info("🔄 Starting subquery generation")
        logger.info(f"📋 Schema fields: {self._extract_schema_fields(schema)}")
        logger.info(f"📝 Instruction: {instruction[:100]}...")

        prompt = f"""Given this JSON schema and extraction instruction, break it down into 1-3 specific, focused subqueries that will help extract the required information.

JSON Schema:
{schema}

Instruction:
{instruction}

Return ONLY a JSON array of 1-3 specific search queries, like:
["query about specific aspect 1", "query about specific aspect 2", "query about specific aspect 3"]

Each query should be focused on extracting specific parts of the schema."""

        try:
            logger.info(f"🤖 Making OpenAI API call with model: {self.llm_model}")
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": "You are a query decomposition expert. Return only valid JSON arrays."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            content = response.choices[0].message.content.strip()
            logger.info(f"📥 Raw API response: {content}")

            # Use safe JSON parsing with healing
            subqueries = safe_json_parse(content, fallback_value=[instruction])

            if isinstance(subqueries, list) and len(subqueries) > 0:
                logger.info(f"✅ Generated {len(subqueries)} subqueries: {subqueries}")
                return subqueries
            else:
                logger.warning("⚠️ Invalid subqueries format, using fallback")
                return [instruction]

        except Exception as e:
            logger.error(f"❌ Error generating subqueries: {e}")
            return [instruction]  # Fallback to original instruction

    def _extract_schema_fields(self, schema: str) -> List[str]:
        """Extract field names from JSON schema for logging."""
        try:
            schema_obj = json.loads(schema)
            if "items" in schema_obj and "properties" in schema_obj["items"]:
                return list(schema_obj["items"]["properties"].keys())
            elif "properties" in schema_obj:
                return list(schema_obj["properties"].keys())
        except:
            pass
        return ["unknown"]

    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI."""
        logger.info(f"🧠 Generating embedding for text: {len(text)} characters")
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            embedding = response.data[0].embedding
            logger.info(f"✅ Generated embedding vector of length: {len(embedding)}")
            return embedding
        except Exception as e:
            logger.error(f"❌ Error generating embedding: {e}")
            return [0.0] * 1536  # Return zero vector as fallback

    def search_documents(self, query: str, file_name: str, top_k: int = 10) -> List[Dict]:
        """Search documents using vector similarity."""
        logger.info(f"🔍 Searching documents for query: '{query[:50]}...'")
        logger.info(f"📁 File filter: {file_name or 'All files'}")
        logger.info(f"🔢 Top K: {top_k}")

        try:
            # Generate embedding for the query
            logger.info("🧠 Generating embedding for search query")
            query_embedding = self.generate_embedding(query)
            logger.info(f"✅ Generated embedding vector of length: {len(query_embedding)}")

            # Call the match_documents function
            logger.info("📡 Calling Supabase match_documents function")
            result = self.supabase.rpc('match_documents', {
                'query_embedding': query_embedding,
                'match_threshold': 0.1,  # Low threshold to get more results
                'match_count': top_k,
                'input_file_name': file_name
            }).execute()

            search_results = result.data if result.data else []
            logger.info(f"📊 Found {len(search_results)} matching documents")

            if search_results:
                avg_similarity = sum(r.get('similarity', 0) for r in search_results) / len(search_results)
                logger.info(f"📈 Average similarity score: {avg_similarity:.3f}")
                logger.info(f"📄 Page range: {min(r.get('page_number', 0) for r in search_results)} - {max(r.get('page_number', 0) for r in search_results)}")

            return search_results

        except Exception as e:
            logger.error(f"❌ Error searching documents: {e}")
            return []

    def extract_structured_data(self, schema: str, instruction: str, context_chunks: List[str]) -> List[Dict]:
        """Extract structured JSON data from context using GPT-4."""
        logger.info("🧠 Starting structured data extraction")
        logger.info(f"📋 Schema fields: {self._extract_schema_fields(schema)}")
        logger.info(f"📝 Instruction: {instruction[:100]}...")
        logger.info(f"📄 Context chunks: {len(context_chunks)}")

        # Combine all context chunks
        combined_context = "\n\n---\n\n".join(context_chunks)

        # Limit context size to avoid token limits
        max_context_length = 15000  # Conservative limit
        original_length = len(combined_context)
        if len(combined_context) > max_context_length:
            combined_context = combined_context[:max_context_length] + "\n\n[Content truncated due to length...]"
            logger.warning(f"⚠️ Context truncated from {original_length} to {len(combined_context)} characters")
        else:
            logger.info(f"📊 Context length: {len(combined_context)} characters")

        prompt = f"""Extract structured data from the following context according to the JSON schema and instruction.

JSON Schema:
{schema}

Instruction:
{instruction}

Context:
{combined_context}

Return a JSON array of objects that conform to the schema. If a value is missing or cannot be determined, use "N/A".
Return ONLY valid JSON - no explanations or additional text.

Example format:
[
  {{"field1": "value1", "field2": "value2"}},
  {{"field1": "value3", "field2": "N/A"}}
]"""

        try:
            logger.info(f"🤖 Making OpenAI API call with model: {self.llm_model}")
            response = self.openai_client.chat.completions.create(
                model=self.llm_model,
                messages=[
                    {"role": "system", "content": "You are a data extraction expert. Return only valid JSON arrays that conform to the given schema."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=4000
            )

            content = response.choices[0].message.content.strip()
            logger.info(f"📥 Raw API response: {len(content)} characters")
            logger.info(f"📄 Response preview: {content[:200]}...")

            # Use safe JSON parsing with healing
            result = safe_json_parse(content, fallback_value=[{"error": "Failed to parse AI response"}])

            if isinstance(result, list):
                logger.info(f"✅ Successfully extracted {len(result)} records")
                # Log sample of extracted data
                if result and not result[0].get("error"):
                    sample_keys = list(result[0].keys()) if result[0] else []
                    logger.info(f"📊 Sample record keys: {sample_keys}")
                return result
            else:
                logger.warning("⚠️ Result is not a list, wrapping in array")
                return [result]

        except Exception as e:
            logger.error(f"❌ Error extracting structured data: {e}")
            error_type = type(e).__name__
            return [{"error": f"Failed to extract data: {error_type} - {str(e)}"}]

    def process_query(self, schema: str, instruction: str, file_name: str) -> Tuple[List[Dict], str]:
        """Main processing function that orchestrates the entire workflow."""
        logger.info("🚀 Starting query processing workflow")
        logger.info(f"📋 Schema: {schema[:100]}...")
        logger.info(f"📝 Instruction: {instruction}")
        logger.info(f"📁 File: {file_name}")

        try:
            # Validate inputs
            if not schema.strip():
                logger.error("❌ Empty schema provided")
                return [], "❌ Please provide a JSON schema"
            if not instruction.strip():
                logger.error("❌ Empty instruction provided")
                return [], "❌ Please provide an instruction"
            if not file_name.strip():
                logger.error("❌ Empty file name provided")
                return [], "❌ Please provide a file name"

            # Validate JSON schema
            try:
                schema_obj = json.loads(schema)
                logger.info(f"✅ Valid JSON schema with fields: {self._extract_schema_fields(schema)}")
            except json.JSONDecodeError as e:
                logger.error(f"❌ Invalid JSON schema: {e}")
                return [], "❌ Invalid JSON schema format"

            status_msg = "🔄 Processing query...\n"

            # Step 1: Generate subqueries
            logger.info("📝 Step 1: Generating subqueries")
            status_msg += "📝 Breaking down instruction into subqueries...\n"
            subqueries = self.generate_subqueries(schema, instruction)
            status_msg += f"Generated {len(subqueries)} subqueries\n"
            logger.info(f"✅ Generated subqueries: {subqueries}")

            # Step 2: Search for each subquery and collect context
            logger.info("🔍 Step 2: Searching for relevant content")
            all_context_chunks = []
            search_stats = {"total_results": 0, "unique_chunks": 0}

            for i, subquery in enumerate(subqueries):
                logger.info(f"🔍 Processing subquery {i+1}/{len(subqueries)}: {subquery}")
                status_msg += f"🔍 Searching for subquery {i+1}: {subquery[:50]}...\n"
                search_results = self.search_documents(subquery, file_name)
                search_stats["total_results"] += len(search_results)

                if search_results:
                    # Extract text from search results
                    for result in search_results:
                        if result.get('text'):
                            all_context_chunks.append(result['text'])
                    status_msg += f"Found {len(search_results)} relevant chunks\n"
                    logger.info(f"✅ Found {len(search_results)} chunks for subquery {i+1}")
                else:
                    status_msg += "No relevant chunks found for this subquery\n"
                    logger.warning(f"⚠️ No results for subquery {i+1}")

            if not all_context_chunks:
                logger.error(f"❌ No relevant content found in '{file_name}'")
                return [], f"{status_msg}❌ No relevant content found in '{file_name}'"

            # Remove duplicates while preserving order
            logger.info("🔄 Step 3: Deduplicating context chunks")
            unique_chunks = []
            seen = set()
            for chunk in all_context_chunks:
                if chunk not in seen:
                    unique_chunks.append(chunk)
                    seen.add(chunk)

            search_stats["unique_chunks"] = len(unique_chunks)
            logger.info(f"📊 Search statistics: {search_stats}")
            status_msg += f"📄 Using {len(unique_chunks)} unique context chunks\n"

            # Step 3: Extract structured data
            logger.info("🧠 Step 4: Extracting structured data")
            status_msg += "🧠 Extracting structured data with GPT-4...\n"
            structured_data = self.extract_structured_data(schema, instruction, unique_chunks)

            # Final validation and logging
            if structured_data and not structured_data[0].get("error"):
                logger.info(f"🎉 Successfully completed query processing: {len(structured_data)} records extracted")
                status_msg += f"✅ Extracted {len(structured_data)} records"
            else:
                logger.warning(f"⚠️ Query completed with issues: {structured_data}")
                status_msg += f"⚠️ Extraction completed with {len(structured_data)} records (may contain errors)"

            return structured_data, status_msg

        except Exception as e:
            logger.error(f"❌ Critical error in query processing: {e}")
            return [], f"❌ Error processing query: {str(e)}"

def create_csv_download(data: List[Dict]) -> str:
    """Create a CSV string from the structured data."""
    if not data:
        return ""
    
    output = io.StringIO()
    if data:
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    return output.getvalue()

def main():
    """Create and launch the Gradio interface."""
    processor = StructuredQueryProcessor()
    
    def process_and_format(schema, instruction, file_name):
        """Process the query and return formatted results."""
        structured_data, status = processor.process_query(schema, instruction, file_name)
        
        # Format JSON for display
        json_output = json.dumps(structured_data, indent=2) if structured_data else "[]"
        
        # Create CSV for download
        csv_data = create_csv_download(structured_data)
        
        return json_output, status, csv_data
    
    # Example schema and instruction
    example_schema = """{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "part_number": {"type": "string"},
      "description": {"type": "string"},
      "price": {"type": "string"},
      "availability": {"type": "string"}
    }
  }
}"""
    
    example_instruction = "Extract all spare parts with their part numbers, descriptions, prices, and availability status from the document."
    
    # Create Gradio interface
    with gr.Blocks(title="Structured Query Extractor", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🔍 Structured Query Extractor")
        gr.Markdown("Extract structured JSON data from PDF documents using natural language instructions and JSON schemas.")
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("## 📝 Input")
                
                schema_input = gr.Textbox(
                    label="JSON Schema",
                    placeholder="Paste your JSON schema here...",
                    lines=10,
                    value=example_schema
                )
                
                instruction_input = gr.Textbox(
                    label="Extraction Instruction",
                    placeholder="Describe what you want to extract...",
                    lines=3,
                    value=example_instruction
                )
                
                file_name_input = gr.Textbox(
                    label="File Name",
                    placeholder="e.g., TANK CLEANING MACHINE.pdf",
                    value="TANK CLEANING MACHINE.pdf"
                )
                
                run_button = gr.Button("🚀 Run Query", variant="primary")
            
            with gr.Column(scale=1):
                gr.Markdown("## 📊 Results")
                
                status_output = gr.Textbox(
                    label="Status",
                    lines=8,
                    interactive=False
                )
                
                json_output = gr.Code(
                    label="Extracted JSON Data",
                    language="json",
                    lines=15
                )
                
                csv_download = gr.File(
                    label="Download CSV",
                    visible=False
                )
        
        # Event handlers
        def handle_query(schema, instruction, file_name):
            json_result, status, csv_data = process_and_format(schema, instruction, file_name)
            
            # Create temporary CSV file for download
            csv_file = None
            if csv_data:
                csv_file = "extracted_data.csv"
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    f.write(csv_data)
            
            return json_result, status, csv_file
        
        run_button.click(
            fn=handle_query,
            inputs=[schema_input, instruction_input, file_name_input],
            outputs=[json_output, status_output, csv_download]
        )
        
        # Show CSV download when data is available
        json_output.change(
            fn=lambda x: gr.update(visible=bool(x and x.strip() != "[]")),
            inputs=[json_output],
            outputs=[csv_download]
        )
    
    return app

if __name__ == "__main__":
    app = main()
    app.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        debug=True
    )

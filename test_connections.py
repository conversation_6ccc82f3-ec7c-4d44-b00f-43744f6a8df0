#!/usr/bin/env python3
"""
Test script to verify API connections.
"""

import os
from dotenv import load_dotenv
from structured_query import StructuredQueryProcessor

load_dotenv()

def main():
    print("🔍 Testing API Connections")
    print("=" * 40)
    
    processor = StructuredQueryProcessor()
    success, message = processor.test_connections()
    
    if success:
        print("✅ All connections working!")
        
        # Test a simple query
        print("\n🧪 Testing simple query...")
        schema = '{"type": "array", "items": {"type": "object", "properties": {"test": {"type": "string"}}}}'
        instruction = "Find any equipment mentioned"
        file_name = "TANK CLEANING MACHINE.pdf"
        
        results, status = processor.process_query(schema, instruction, file_name)
        print("Query test result:", "✅ Success" if results else "❌ Failed")
        print("Status:", status.split('\n')[-1])
        
    else:
        print(f"❌ Connection failed: {message}")

if __name__ == "__main__":
    main()

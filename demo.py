#!/usr/bin/env python3
"""
Demo script showing how to use the PDF processing and structured query system.

This script demonstrates:
1. How to process a PDF programmatically
2. How to run structured queries
3. Different schema examples
4. Error handling
"""

import json
import os
from dotenv import load_dotenv
from ingest_pdf import PDFProcessor
from structured_query import StructuredQueryProcessor

# Load environment variables
load_dotenv()

def demo_pdf_ingestion():
    """Demonstrate PDF ingestion process."""
    print("🔄 PDF Ingestion Demo")
    print("=" * 50)
    
    processor = PDFProcessor()
    
    # Check if sample PDF exists
    sample_pdf = "sample/TANK CLEANING MACHINE.pdf"
    if not os.path.exists(sample_pdf):
        print(f"❌ Sample PDF not found: {sample_pdf}")
        return False
    
    print(f"📄 Processing: {sample_pdf}")
    
    # Process the PDF (this will take a while for a large PDF)
    # Uncomment the next line if you want to re-process the PDF
    # success = processor.process_pdf(sample_pdf)
    
    # For demo purposes, we'll assume it's already processed
    success = True
    print("✅ PDF processing completed (or already processed)")
    
    # Print summary
    processor.print_summary()
    
    return success

def demo_structured_queries():
    """Demonstrate various structured query examples."""
    print("\n🔍 Structured Query Demo")
    print("=" * 50)
    
    processor = StructuredQueryProcessor()
    file_name = "TANK CLEANING MACHINE.pdf"
    
    # Demo 1: Equipment Information
    print("\n📋 Demo 1: Equipment Information Extraction")
    print("-" * 40)
    
    schema_1 = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "equipment_name": {"type": "string"},
                "model": {"type": "string"},
                "specifications": {"type": "string"},
                "application": {"type": "string"}
            }
        }
    }
    
    instruction_1 = "Extract information about tank cleaning equipment, including equipment names, models, technical specifications, and applications."
    
    print(f"Schema: {list(schema_1['items']['properties'].keys())}")
    print(f"Instruction: {instruction_1}")
    print("Processing...")
    
    try:
        results_1, status_1 = processor.process_query(
            json.dumps(schema_1, indent=2), 
            instruction_1, 
            file_name
        )
        
        print("Status:", status_1.split('\n')[-1])  # Last line of status
        print(f"Results: {len(results_1)} items extracted")
        if results_1:
            print("Sample result:")
            print(json.dumps(results_1[0], indent=2))
    
    except Exception as e:
        print(f"Error: {e}")
    
    # Demo 2: Technical Specifications
    print("\n📋 Demo 2: Technical Specifications")
    print("-" * 40)
    
    schema_2 = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "parameter": {"type": "string"},
                "value": {"type": "string"},
                "unit": {"type": "string"},
                "description": {"type": "string"}
            }
        }
    }
    
    instruction_2 = "Extract technical specifications including parameters, values, units, and descriptions from the equipment documentation."
    
    print(f"Schema: {list(schema_2['items']['properties'].keys())}")
    print(f"Instruction: {instruction_2}")
    print("Processing...")
    
    try:
        results_2, status_2 = processor.process_query(
            json.dumps(schema_2, indent=2), 
            instruction_2, 
            file_name
        )
        
        print("Status:", status_2.split('\n')[-1])  # Last line of status
        print(f"Results: {len(results_2)} items extracted")
        if results_2:
            print("Sample result:")
            print(json.dumps(results_2[0], indent=2))
    
    except Exception as e:
        print(f"Error: {e}")
    
    # Demo 3: Safety Information
    print("\n📋 Demo 3: Safety Information")
    print("-" * 40)
    
    schema_3 = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "safety_topic": {"type": "string"},
                "warning_level": {"type": "string"},
                "description": {"type": "string"},
                "precautions": {"type": "string"}
            }
        }
    }
    
    instruction_3 = "Extract safety information including warnings, precautions, and safety procedures from the documentation."
    
    print(f"Schema: {list(schema_3['items']['properties'].keys())}")
    print(f"Instruction: {instruction_3}")
    print("Processing...")
    
    try:
        results_3, status_3 = processor.process_query(
            json.dumps(schema_3, indent=2), 
            instruction_3, 
            file_name
        )
        
        print("Status:", status_3.split('\n')[-1])  # Last line of status
        print(f"Results: {len(results_3)} items extracted")
        if results_3:
            print("Sample result:")
            print(json.dumps(results_3[0], indent=2))
    
    except Exception as e:
        print(f"Error: {e}")

def demo_error_handling():
    """Demonstrate error handling scenarios."""
    print("\n⚠️  Error Handling Demo")
    print("=" * 50)
    
    processor = StructuredQueryProcessor()
    
    # Test cases
    test_cases = [
        {
            "name": "Empty Schema",
            "schema": "",
            "instruction": "Extract something",
            "file_name": "test.pdf"
        },
        {
            "name": "Invalid JSON Schema",
            "schema": "invalid json",
            "instruction": "Extract something",
            "file_name": "test.pdf"
        },
        {
            "name": "Non-existent File",
            "schema": '{"type": "object", "properties": {"test": {"type": "string"}}}',
            "instruction": "Extract test data",
            "file_name": "nonexistent.pdf"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        try:
            results, status = processor.process_query(
                test_case['schema'],
                test_case['instruction'],
                test_case['file_name']
            )
            print(f"Result: {status}")
        except Exception as e:
            print(f"Exception: {e}")

def main():
    """Run the complete demo."""
    print("🚀 PDF Processing & Structured Query Demo")
    print("=" * 60)
    
    # Check environment variables
    required_vars = ["OPENAI_API_KEY", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
        return
    
    print("✅ Environment variables configured")
    
    # Run demos
    try:
        # Demo PDF ingestion (commented out to avoid re-processing)
        # demo_pdf_ingestion()
        
        # Demo structured queries
        demo_structured_queries()
        
        # Demo error handling
        demo_error_handling()
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 Next steps:")
        print("1. Run 'python structured_query.py' to launch the Gradio interface")
        print("2. Try different schemas and instructions")
        print("3. Export results as CSV files")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

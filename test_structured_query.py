#!/usr/bin/env python3
"""
Test script for the structured query functionality.

This script tests the core components of the structured query processor
without the Gradio interface.
"""

import json
import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from structured_query import StructuredQueryProcessor

# Load environment variables
load_dotenv()

def test_basic_functionality():
    """Test basic functionality of the StructuredQueryProcessor."""
    print("🧪 Testing Structured Query Processor")
    print("=" * 50)
    
    processor = StructuredQueryProcessor()
    
    # Test schema
    test_schema = """{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "equipment_name": {"type": "string"},
      "model": {"type": "string"},
      "specifications": {"type": "string"},
      "features": {"type": "string"}
    }
  }
}"""
    
    # Test instruction
    test_instruction = "Extract information about tank cleaning equipment, including equipment names, models, specifications, and key features."
    
    # Test file name
    test_file_name = "TANK CLEANING MACHINE.pdf"
    
    print(f"📋 Schema: {json.loads(test_schema)['items']['properties'].keys()}")
    print(f"📝 Instruction: {test_instruction[:80]}...")
    print(f"📄 File: {test_file_name}")
    print()
    
    # Test subquery generation
    print("🔍 Testing subquery generation...")
    subqueries = processor.generate_subqueries(test_schema, test_instruction)
    print(f"Generated {len(subqueries)} subqueries:")
    for i, query in enumerate(subqueries, 1):
        print(f"  {i}. {query}")
    print()
    
    # Test embedding generation
    print("🧠 Testing embedding generation...")
    test_text = "tank cleaning machine specifications"
    embedding = processor.generate_embedding(test_text)
    print(f"Generated embedding vector of length: {len(embedding)}")
    print(f"First 5 values: {embedding[:5]}")
    print()
    
    # Test document search
    print("🔍 Testing document search...")
    search_results = processor.search_documents("tank cleaning equipment", test_file_name, top_k=3)
    print(f"Found {len(search_results)} search results")
    if search_results:
        for i, result in enumerate(search_results[:2], 1):
            print(f"  Result {i}:")
            print(f"    Page: {result.get('page_number', 'N/A')}")
            print(f"    Similarity: {result.get('similarity', 'N/A'):.3f}")
            print(f"    Text preview: {result.get('text', '')[:100]}...")
    print()
    
    # Test full processing
    print("🚀 Testing full query processing...")
    structured_data, status = processor.process_query(test_schema, test_instruction, test_file_name)
    
    print("Status:")
    print(status)
    print()
    
    print("Structured Data:")
    print(json.dumps(structured_data, indent=2))
    print()
    
    print(f"✅ Extracted {len(structured_data)} records")
    
    return len(structured_data) > 0

def test_error_handling():
    """Test error handling scenarios."""
    print("🧪 Testing Error Handling")
    print("=" * 50)
    
    processor = StructuredQueryProcessor()
    
    # Test with empty inputs
    print("Testing empty inputs...")
    result, status = processor.process_query("", "", "")
    print(f"Empty inputs result: {status}")
    assert "Please provide" in status
    
    # Test with invalid JSON schema
    print("Testing invalid JSON schema...")
    result, status = processor.process_query("invalid json", "test instruction", "test.pdf")
    print(f"Invalid JSON result: {status}")
    assert "Invalid JSON schema" in status
    
    # Test with non-existent file
    print("Testing non-existent file...")
    valid_schema = '{"type": "object", "properties": {"test": {"type": "string"}}}'
    result, status = processor.process_query(valid_schema, "test instruction", "nonexistent.pdf")
    print(f"Non-existent file result: {status}")
    
    print("✅ Error handling tests completed")

def test_csv_generation():
    """Test CSV generation functionality."""
    print("🧪 Testing CSV Generation")
    print("=" * 50)
    
    from structured_query import create_csv_download
    
    # Test data
    test_data = [
        {"name": "Item 1", "price": "$100", "description": "Test item 1"},
        {"name": "Item 2", "price": "$200", "description": "Test item 2"}
    ]
    
    csv_output = create_csv_download(test_data)
    print("Generated CSV:")
    print(csv_output)
    
    # Verify CSV structure
    lines = csv_output.strip().split('\n')
    assert len(lines) == 3  # Header + 2 data rows
    assert "name,price,description" in lines[0]
    
    print("✅ CSV generation test passed")

def main():
    """Run all tests."""
    print("🚀 Starting Structured Query Tests")
    print("=" * 60)
    
    # Check environment variables
    required_vars = ["OPENAI_API_KEY", "SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    try:
        # Run tests
        success = True
        
        success &= test_basic_functionality()
        print()
        
        test_error_handling()
        print()
        
        test_csv_generation()
        print()
        
        if success:
            print("🎉 All tests completed successfully!")
            return True
        else:
            print("⚠️  Some tests had issues, but error handling worked correctly.")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
